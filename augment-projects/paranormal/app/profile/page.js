'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSearchParams } from 'next/navigation';
import Navigation from '../components/Navigation';

export default function ProfilePage() {
  const { user, updateUser, isAuthenticated, isLoading } = useAuth();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('personal');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    facebookUrl: '',
    gender: '',
    age: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  // Phone verification states
  const [isPhoneModalOpen, setIsPhoneModalOpen] = useState(false);
  const [newPhone, setNewPhone] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  // Password change states
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Auto-dismiss notification after 5 seconds
  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => {
        setMessage({ type: '', text: '' });
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [message.text]);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        facebookUrl: user.facebookUrl || '',
        gender: user.gender || '',
        age: user.age || ''
      });
    }
  }, [user]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['personal', 'security', 'account'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      // Prepare data for submission - convert empty strings to null for numeric fields
      const submitData = {
        ...formData,
        age: formData.age === '' ? null : parseInt(formData.age) || null
      };

      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      const data = await response.json();

      if (data.success) {
        updateUser(data.user);
        setIsEditing(false);
        setMessage({ type: 'success', text: 'პროფილი წარმატებით განახლდა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'პროფილის განახლება ვერ მოხერხდა' });
      }
    } catch (error) {
      console.error('Profile update error:', error);
      setMessage({ type: 'error', text: 'პროფილის განახლება ვერ მოხერხდა' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setMessage({ type: '', text: '' });
    // Reset form data to original user data
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        facebookUrl: user.facebookUrl || '',
        gender: user.gender || '',
        age: user.age || ''
      });
    }
  };

  // Phone verification functions
  const handlePhoneChange = () => {
    setIsPhoneModalOpen(true);
    setNewPhone('');
    setVerificationCode('');
    setIsVerificationSent(false);
  };

  const sendPhoneVerification = async () => {
    if (!newPhone || newPhone.length < 9) {
      setMessage({ type: 'error', text: 'გთხოვთ შეიყვანოთ სწორი ტელეფონის ნომერი' });
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch('/api/auth/send-phone-verification', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ phone: newPhone })
      });

      const data = await response.json();
      if (data.success) {
        setIsVerificationSent(true);
        setMessage({ type: 'success', text: 'ვერიფიკაციის კოდი გაიგზავნა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'კოდის გაგზავნა ვერ მოხერხდა' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'კოდის გაგზავნა ვერ მოხერხდა' });
    } finally {
      setIsVerifying(false);
    }
  };

  const verifyPhoneChange = async () => {
    if (verificationCode.length !== 6) {
      setMessage({ type: 'error', text: 'გთხოვთ შეიყვანოთ 6-ნიშნა კოდი' });
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch('/api/auth/verify-phone-change', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phone: newPhone,
          verificationCode
        })
      });

      const data = await response.json();
      if (data.success) {
        updateUser(data.user);
        setIsPhoneModalOpen(false);
        setMessage({ type: 'success', text: 'ტელეფონის ნომერი წარმატებით შეიცვალა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'ვერიფიკაცია ვერ მოხერხდა' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'ვერიფიკაცია ვერ მოხერხდა' });
    } finally {
      setIsVerifying(false);
    }
  };

  // Password change functions
  const handlePasswordChange = async () => {
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'გთხოვთ შეავსოთ ყველა ველი' });
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'ახალი პაროლები არ ემთხვევა' });
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setMessage({ type: 'error', text: 'პაროლი უნდა იყოს მინიმუმ 6 სიმბოლო' });
      return;
    }

    setIsChangingPassword(true);
    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      });

      const data = await response.json();
      if (data.success) {
        setIsPasswordModalOpen(false);
        setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
        setMessage({ type: 'success', text: 'პაროლი წარმატებით შეიცვალა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'პაროლის შეცვლა ვერ მოხერხდა' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'პაროლის შეცვლა ვერ მოხერხდა' });
    } finally {
      setIsChangingPassword(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        <div className="flex items-center justify-center min-h-[60vh] pt-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              ავტორიზაცია საჭიროა
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              პროფილის სანახავად გთხოვთ შეხვიდეთ სისტემაში
            </p>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'personal', name: 'პირადი ინფორმაცია', icon: 'user' },
    { id: 'security', name: 'უსაფრთხოება', icon: 'shield' },
    { id: 'account', name: 'ანგარიშის ინფო', icon: 'info' }
  ];

  const getTabIcon = (iconType) => {
    switch (iconType) {
      case 'user':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        );
      case 'shield':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Modern Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-indigo-950"></div>

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-primary/20 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-accent/30 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-mystery/25 rounded-full animate-pulse delay-2000"></div>
      </div>

      <Navigation />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-12 relative z-10">
        {/* Page Header */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-4">
            ჩემი პროფილი
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            მართეთ თქვენი პირადი ინფორმაცია და ანგარიშის პარამეტრები
          </p>
        </div>

        {/* Profile Card */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 overflow-hidden mb-8">
          {/* Profile Header */}
          <div className="px-4 sm:px-8 py-6 sm:py-8 bg-gradient-to-r from-primary/5 to-accent/5 dark:from-primary/10 dark:to-accent/10 border-b border-gray-200/50 dark:border-gray-700/50">
            <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="relative">
                <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-primary to-primary-dark text-white rounded-2xl flex items-center justify-center text-xl sm:text-2xl font-bold shadow-lg">
                  {user?.avatar ? (
                    <img src={user.avatar} alt={user.name} className="w-20 h-20 sm:w-24 sm:h-24 rounded-2xl object-cover" />
                  ) : (
                    user?.name?.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2) ||
                    (user?.firstName && user?.lastName ? `${user.firstName[0]}${user.lastName[0]}` : 'U')
                  )}
                </div>
                {user?.isVerified && (
                  <div className="absolute -bottom-1 -right-1 sm:-bottom-2 sm:-right-2 w-6 h-6 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-3 h-3 sm:w-5 sm:h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </div>

              <div className="flex-1 text-center sm:text-left">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {user?.firstName && user?.lastName
                    ? `${user.firstName} ${user.lastName}`
                    : user?.name || 'მომხმარებელი'
                  }
                </h2>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-3">
                  {user?.phone || 'ტელეფონი არ არის მითითებული'}
                </p>
                <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-2 sm:space-y-0 sm:space-x-6">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-base sm:text-lg font-semibold text-green-600 dark:text-green-400">
                      ₾{user?.balance?.toFixed(2) || '0.00'}
                    </span>
                  </div>
                  {user?.isVerified && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      ვერიფიცირებული
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Tabs Navigation */}
          <div className="px-4 sm:px-8 py-4 bg-gray-50/50 dark:bg-gray-900/50 border-b border-gray-200/50 dark:border-gray-700/50">
            {/* Desktop Tabs */}
            <nav className="hidden sm:flex space-x-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`profile-tab flex items-center space-x-2 px-4 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 ${
                    activeTab === tab.id
                      ? 'bg-white dark:bg-gray-800 text-primary dark:text-primary shadow-lg border border-primary/20 scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-800/50'
                  }`}
                >
                  {getTabIcon(tab.icon)}
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>

            {/* Mobile Tabs - Dropdown Style */}
            <div className="sm:hidden">
              <div className="relative">
                <select
                  value={activeTab}
                  onChange={(e) => setActiveTab(e.target.value)}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-900 dark:text-white font-medium focus:ring-2 focus:ring-primary focus:border-transparent appearance-none"
                >
                  {tabs.map((tab) => (
                    <option key={tab.id} value={tab.id}>
                      {tab.name}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Alternative Mobile Tabs - Horizontal Scroll */}
            <div className="sm:hidden hidden">
              <div className="flex space-x-2 overflow-x-auto pb-2 scrollbar-hide">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`profile-tab flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-300 whitespace-nowrap flex-shrink-0 ${
                      activeTab === tab.id
                        ? 'bg-white dark:bg-gray-800 text-primary dark:text-primary shadow-md border border-primary/20'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-800/50'
                    }`}
                  >
                    {getTabIcon(tab.icon)}
                    <span className="text-sm">{tab.name}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 sm:p-8">
            {message.text && (
              <div className={`mb-6 relative overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-500 transform animate-slide-down ${
                message.type === 'success'
                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-800 border-green-200/50 dark:from-green-900/30 dark:to-emerald-900/30 dark:text-green-300 dark:border-green-700/50 shadow-green-500/10'
                  : 'bg-gradient-to-r from-red-50 to-rose-50 text-red-800 border-red-200/50 dark:from-red-900/30 dark:to-rose-900/30 dark:text-red-300 dark:border-red-700/50 shadow-red-500/10'
              } shadow-lg hover:shadow-xl`}>

                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute inset-0" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
                    backgroundSize: '40px 40px'
                  }}></div>
                </div>

                {/* Animated Border */}
                <div className={`absolute inset-0 rounded-2xl ${
                  message.type === 'success'
                    ? 'bg-gradient-to-r from-green-400/20 via-emerald-400/20 to-green-400/20'
                    : 'bg-gradient-to-r from-red-400/20 via-rose-400/20 to-red-400/20'
                } animate-pulse`}></div>

                <div className="relative p-5">
                  <div className="flex items-start space-x-4">
                    {/* Icon Container */}
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      message.type === 'success'
                        ? 'bg-green-100 dark:bg-green-800/50 text-green-600 dark:text-green-400'
                        : 'bg-red-100 dark:bg-red-800/50 text-red-600 dark:text-red-400'
                    } shadow-lg transform transition-transform duration-300 hover:scale-110`}>
                      {message.type === 'success' ? (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className={`text-sm font-semibold mb-1 ${
                            message.type === 'success'
                              ? 'text-green-800 dark:text-green-300'
                              : 'text-red-800 dark:text-red-300'
                          }`}>
                            {message.type === 'success' ? '✨ წარმატება!' : '⚠️ შეცდომა!'}
                          </h4>
                          <p className={`text-sm leading-relaxed ${
                            message.type === 'success'
                              ? 'text-green-700 dark:text-green-400'
                              : 'text-red-700 dark:text-red-400'
                          }`}>
                            {message.text}
                          </p>
                        </div>

                        {/* Close Button */}
                        <button
                          onClick={() => setMessage({ type: '', text: '' })}
                          className={`ml-4 flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 ${
                            message.type === 'success'
                              ? 'text-green-600 hover:bg-green-100 dark:text-green-400 dark:hover:bg-green-800/50'
                              : 'text-red-600 hover:bg-red-100 dark:text-red-400 dark:hover:bg-red-800/50'
                          }`}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar for Auto-dismiss */}
                  <div className={`mt-4 h-1 rounded-full overflow-hidden ${
                    message.type === 'success'
                      ? 'bg-green-200 dark:bg-green-800'
                      : 'bg-red-200 dark:bg-red-800'
                  }`}>
                    <div className={`h-full rounded-full ${
                      message.type === 'success'
                        ? 'bg-green-500 dark:bg-green-400'
                        : 'bg-red-500 dark:bg-red-400'
                    } animate-progress-bar`}></div>
                  </div>
                </div>

                {/* Floating Particles */}
                <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
                  <div className={`absolute top-2 right-4 w-1 h-1 rounded-full ${
                    message.type === 'success' ? 'bg-green-400' : 'bg-red-400'
                  } animate-ping`}></div>
                  <div className={`absolute bottom-3 left-6 w-0.5 h-0.5 rounded-full ${
                    message.type === 'success' ? 'bg-emerald-400' : 'bg-rose-400'
                  } animate-pulse delay-500`}></div>
                  <div className={`absolute top-1/2 right-8 w-0.5 h-0.5 rounded-full ${
                    message.type === 'success' ? 'bg-green-300' : 'bg-red-300'
                  } animate-pulse delay-1000`}></div>
                </div>
              </div>
            )}

            {/* Tab Content */}
            {activeTab === 'personal' && (
              <div className="space-y-6 animate-fade-in-up">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    პირადი ინფორმაცია
                  </h3>
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center justify-center space-x-2 bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-xl font-medium transition-colors w-full sm:w-auto"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      <span>რედაქტირება</span>
                    </button>
                  ) : (
                    <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                      <button
                        onClick={handleSave}
                        disabled={isSaving}
                        className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-xl font-medium transition-colors disabled:opacity-50 w-full sm:w-auto"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>{isSaving ? 'შენახვა...' : 'შენახვა'}</span>
                      </button>
                      <button
                        onClick={handleCancel}
                        className="flex items-center justify-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-xl font-medium transition-colors w-full sm:w-auto"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <span>გაუქმება</span>
                      </button>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      სახელი
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                      />
                    ) : (
                      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl text-gray-900 dark:text-white">
                        {user?.firstName || '-'}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      გვარი
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                      />
                    ) : (
                      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl text-gray-900 dark:text-white">
                        {user?.lastName || '-'}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Facebook URL
                    </label>
                    {isEditing ? (
                      <input
                        type="url"
                        name="facebookUrl"
                        value={formData.facebookUrl}
                        onChange={handleInputChange}
                        placeholder="https://facebook.com/username"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                      />
                    ) : (
                      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl text-gray-900 dark:text-white">
                        {user?.facebookUrl || '-'}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      სქესი
                    </label>
                    {isEditing ? (
                      <select
                        name="gender"
                        value={formData.gender}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                      >
                        <option value="">აირჩიეთ სქესი</option>
                        <option value="male">მამრობითი</option>
                        <option value="female">მდედრობითი</option>
                        <option value="other">სხვა</option>
                      </select>
                    ) : (
                      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl text-gray-900 dark:text-white">
                        {user?.gender === 'male' ? 'მამრობითი' :
                         user?.gender === 'female' ? 'მდედრობითი' :
                         user?.gender === 'other' ? 'სხვა' : '-'}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      ასაკი
                    </label>
                    {isEditing ? (
                      <input
                        type="number"
                        name="age"
                        value={formData.age}
                        onChange={handleInputChange}
                        min="16"
                        max="100"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                      />
                    ) : (
                      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl text-gray-900 dark:text-white">
                        {user?.age || '-'}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="space-y-6 animate-fade-in-up">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                  უსაფრთხოება
                </h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      ტელეფონის ნომერი
                    </label>
                    <div className="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                      <span className="text-gray-900 dark:text-white">{user?.phone || '-'}</span>
                      <button
                        onClick={handlePhoneChange}
                        className="text-sm text-primary hover:text-primary-dark font-medium transition-colors"
                      >
                        შეცვლა
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      პაროლი
                    </label>
                    <div className="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                      <span className="text-gray-900 dark:text-white">••••••••</span>
                      <button
                        onClick={() => setIsPasswordModalOpen(true)}
                        className="text-sm text-primary hover:text-primary-dark font-medium transition-colors"
                      >
                        შეცვლა
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Account Info Tab */}
            {activeTab === 'account' && (
              <div className="space-y-6 animate-fade-in-up">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                  ანგარიშის ინფორმაცია
                </h3>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      ბალანსი
                    </label>
                    <div className="px-4 py-3 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
                      <span className="text-lg font-semibold text-green-600 dark:text-green-400">
                        ₾{user?.balance?.toFixed(2) || '0.00'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      ვერიფიკაციის სტატუსი
                    </label>
                    <div className={`px-4 py-3 rounded-xl border ${
                      user?.isVerified
                        ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                        : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                    }`}>
                      <span className={`font-medium ${
                        user?.isVerified
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-yellow-600 dark:text-yellow-400'
                      }`}>
                        {user?.isVerified ? '✓ ვერიფიცირებული' : '⚠️ არ არის ვერიფიცირებული'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      რეგისტრაციის თარიღი
                    </label>
                    <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 rounded-xl text-gray-900 dark:text-white">
                      {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('ka-GE') : '-'}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Phone Verification Modal */}
      {isPhoneModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              ტელეფონის ნომრის შეცვლა
            </h3>

            {!isVerificationSent ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ახალი ტელეფონის ნომერი
                  </label>
                  <input
                    type="tel"
                    value={newPhone}
                    onChange={(e) => setNewPhone(e.target.value)}
                    placeholder="5XXXXXXXX"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setIsPhoneModalOpen(false)}
                    className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    გაუქმება
                  </button>
                  <button
                    onClick={sendPhoneVerification}
                    disabled={isVerifying}
                    className="flex-1 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isVerifying ? 'გაგზავნა...' : 'კოდის გაგზავნა'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    ვერიფიკაციის კოდი გაიგზავნა ნომერზე:
                  </p>
                  <p className="font-semibold text-gray-800 dark:text-white mb-6">
                    {newPhone}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                    შეიყვანეთ 6-ნიშნა კოდი
                  </label>
                  <input
                    type="text"
                    maxLength="6"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                    className="verification-input w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white text-center text-lg font-bold letter-spacing-wide"
                    placeholder="123456"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setIsPhoneModalOpen(false);
                      setIsVerificationSent(false);
                    }}
                    className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    გაუქმება
                  </button>
                  <button
                    onClick={verifyPhoneChange}
                    disabled={isVerifying || verificationCode.length !== 6}
                    className="flex-1 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isVerifying ? 'დადასტურება...' : 'დადასტურება'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Password Change Modal */}
      {isPasswordModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              პაროლის შეცვლა
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  მიმდინარე პაროლი
                </label>
                <input
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ახალი პაროლი
                </label>
                <input
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ახალი პაროლის დადასტურება
                </label>
                <input
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => {
                    setIsPasswordModalOpen(false);
                    setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
                  }}
                  className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  გაუქმება
                </button>
                <button
                  onClick={handlePasswordChange}
                  disabled={isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                  className="flex-1 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  {isChangingPassword ? 'შეცვლა...' : 'შეცვლა'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
